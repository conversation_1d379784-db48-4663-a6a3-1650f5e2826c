#!/usr/bin/env python3
"""
Database script to update barcodes in shProducts collection.

This script:
1. Connects to the database test collection shProducts
2. Finds all records where tcgItem is true and username is CapeFearCollectibles
3. Looks up corresponding records in catalog collection using productId
4. Updates variants.barcode in shProducts with upc or gtin from catalog
5. Provides detailed logging for the process
"""

import pymongo
import logging
from typing import Optional, Dict, Any
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('barcode_update.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BarcodeUpdater:
    def __init__(self, connection_string: str = "mongodb://localhost:27017/"):
        """Initialize the database connection."""
        try:
            self.client = pymongo.MongoClient(connection_string)
            self.db = self.client['test']
            self.sh_products = self.db['shProducts']
            self.catalog = self.db['catalog']
            logger.info(f"Connected to MongoDB at {connection_string}")
            logger.info(f"Database: {self.db.name}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    def find_tcg_products(self) -> list:
        """Find all products where tcgItem is true and username is CapeFearCollectibles."""
        try:
            query = {
                "tcgItem": True,
                "username": "CapeFearCollectibles"
            }
            
            products = list(self.sh_products.find(query))
            logger.info(f"Found {len(products)} products with tcgItem=true and username=CapeFearCollectibles")
            
            for product in products:
                logger.info(f"Product ID: {product.get('_id')}, productId: {product.get('productId')}")
            
            return products
        except Exception as e:
            logger.error(f"Error finding TCG sealed products: {e}")
            return []

    def find_catalog_record(self, product_id: int) -> Optional[Dict[Any, Any]]:
        """Find catalog record by productId."""
        try:
            catalog_record = self.catalog.find_one({"productId": product_id})
            if catalog_record:
                logger.info(f"Found catalog record for productId {product_id}")
                logger.info(f"Catalog record UPC: {catalog_record.get('upc')}")
                logger.info(f"Catalog record GTIN: {catalog_record.get('gtin')}")
            else:
                logger.warning(f"No catalog record found for productId {product_id}")
            return catalog_record
        except Exception as e:
            logger.error(f"Error finding catalog record for productId {product_id}: {e}")
            return None

    def get_barcode_value(self, catalog_record: Dict[Any, Any]) -> Optional[str]:
        """Extract UPC or GTIN from catalog record, preferring UPC."""
        upc = catalog_record.get('upc')
        gtin = catalog_record.get('gtin')
        
        # Prefer UPC over GTIN
        if upc:
            logger.info(f"Using UPC: {upc}")
            return str(upc)
        elif gtin:
            logger.info(f"Using GTIN: {gtin}")
            return str(gtin)
        else:
            logger.warning("No UPC or GTIN found in catalog record")
            return None

    def update_product_barcode(self, product_id: str, barcode: str) -> bool:
        """Update the variants.barcode field in shProducts."""
        try:
            update_query = {"_id": product_id}
            update_operation = {
                "$set": {
                    "variants.barcode": barcode
                }
            }
            
            result = self.sh_products.update_one(update_query, update_operation)
            
            if result.modified_count > 0:
                logger.info(f"Successfully updated product {product_id} with barcode: {barcode}")
                return True
            else:
                logger.warning(f"No document was modified for product {product_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating product {product_id}: {e}")
            return False

    def process_single_product(self) -> bool:
        """Process one product and update its barcode."""
        logger.info("Starting single product processing...")
        
        # Find TCG products
        products = self.find_tcg_products()
        
        if not products:
            logger.error("No TCG products found")
            return False
        
        # Process the first product
        product = products[0]
        product_id = product.get('_id')
        product_id_int = product.get('productId')
        
        logger.info(f"Processing product: {product_id}")
        logger.info(f"Product details: {product}")
        
        if not product_id_int:
            logger.error(f"Product {product_id} has no productId field")
            return False
        
        # Find corresponding catalog record
        catalog_record = self.find_catalog_record(int(product_id_int))
        
        if not catalog_record:
            logger.error(f"No catalog record found for productId {product_id_int}")
            return False
        
        logger.info(f"Catalog record details: {catalog_record}")
        
        # Get barcode value (UPC or GTIN)
        barcode = self.get_barcode_value(catalog_record)
        
        if not barcode:
            logger.error("No valid barcode found in catalog record")
            return False
        
        # Update the product
        success = self.update_product_barcode(product_id, barcode)
        
        if success:
            # Verify the update
            updated_product = self.sh_products.find_one({"_id": product_id})
            logger.info(f"Updated product verification: {updated_product}")
            logger.info(f"New barcode value: {updated_product.get('variants', {}).get('barcode')}")
        
        return success

    def close_connection(self):
        """Close the database connection."""
        if hasattr(self, 'client'):
            self.client.close()
            logger.info("Database connection closed")

def main():
    """Main function to run the barcode updater."""
    # MongoDB connection string with authentication
    connection_string = "*******************************************************************"
    
    updater = None
    try:
        updater = BarcodeUpdater(connection_string)
        success = updater.process_single_product()
        
        if success:
            logger.info("Barcode update completed successfully!")
        else:
            logger.error("Barcode update failed!")
            
    except Exception as e:
        logger.error(f"Script execution failed: {e}")
    finally:
        if updater:
            updater.close_connection()

if __name__ == "__main__":
    main()
