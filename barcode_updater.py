#!/usr/bin/env python3
"""
Database script to update barcodes in shProducts collection.

This script:
1. Connects to the database test collection shProducts
2. Finds all records where tcgItem is true and username is CapeFearCollectibles
3. Looks up corresponding records in catalog collection using productId
4. Updates variants.barcode in shProducts with upc or gtin from catalog
5. Provides detailed logging for the process
"""

import pymongo
import logging
# Removed unused imports
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('barcode_update.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BarcodeUpdater:
    def __init__(self, connection_string: str = "mongodb://localhost:27017/"):
        """Initialize the database connection."""
        try:
            self.client = pymongo.MongoClient(connection_string)
            self.db = self.client['test']
            self.sh_products = self.db['shProducts']
            self.catalog = self.db['catalog']
            logger.info(f"Connected to MongoDB at {connection_string}")
            logger.info(f"Database: {self.db.name}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    def find_tcg_products_with_catalog(self) -> list:
        """Find TCG products with their catalog data using aggregation pipeline."""
        try:
            pipeline = [
                # Match products with tcgItem=true and username=CapeFearCollectibles
                {
                    "$match": {
                        "tcgItem": True,
                        "username": "CapeFearCollectibles"
                    }
                },
                # Convert productId to integer for lookup
                {
                    "$addFields": {
                        "productIdInt": {"$toInt": "$productId"}
                    }
                },
                # Lookup catalog data
                {
                    "$lookup": {
                        "from": "catalog",
                        "localField": "productIdInt",
                        "foreignField": "productId",
                        "as": "catalogData"
                    }
                },
                # Only keep products that have catalog data
                {
                    "$match": {
                        "catalogData": {"$ne": []}
                    }
                },
                # Unwind catalog data (should be only one match)
                {
                    "$unwind": "$catalogData"
                },
                # Add barcode field from catalog (prefer UPC over GTIN)
                {
                    "$addFields": {
                        "newBarcode": {
                            "$cond": {
                                "if": {"$ne": ["$catalogData.upc", None]},
                                "then": {"$toString": "$catalogData.upc"},
                                "else": {
                                    "$cond": {
                                        "if": {"$ne": ["$catalogData.gtin", None]},
                                        "then": {"$toString": "$catalogData.gtin"},
                                        "else": None
                                    }
                                }
                            }
                        }
                    }
                },
                # Only keep products that have a valid barcode
                {
                    "$match": {
                        "newBarcode": {"$ne": None}
                    }
                },
                # Project only needed fields
                {
                    "$project": {
                        "_id": 1,
                        "productId": 1,
                        "username": 1,
                        "tcgItem": 1,
                        "variants": 1,
                        "catalogData.upc": 1,
                        "catalogData.gtin": 1,
                        "newBarcode": 1
                    }
                }
            ]

            products = list(self.sh_products.aggregate(pipeline))
            logger.info(f"Found {len(products)} TCG products with valid catalog data and barcodes")

            for product in products:
                logger.info(f"Product ID: {product.get('_id')}, productId: {product.get('productId')}, newBarcode: {product.get('newBarcode')}")

            return products
        except Exception as e:
            logger.error(f"Error finding TCG products with catalog data: {e}")
            return []

    def bulk_update_barcodes(self, products: list) -> int:
        """Bulk update barcodes for multiple products."""
        try:
            from pymongo import UpdateOne

            bulk_operations = []
            for product in products:
                product_id = product.get('_id')
                new_barcode = product.get('newBarcode')

                if product_id and new_barcode:
                    operation = UpdateOne(
                        {"_id": product_id},
                        {"$set": {"variants.barcode": new_barcode}}
                    )
                    bulk_operations.append(operation)

            if bulk_operations:
                result = self.sh_products.bulk_write(bulk_operations)
                logger.info(f"Bulk update completed: {result.modified_count} documents updated")
                return result.modified_count
            else:
                logger.warning("No valid operations to perform")
                return 0

        except Exception as e:
            logger.error(f"Error performing bulk update: {e}")
            return 0

    def update_product_barcode(self, product_id: str, barcode: str) -> bool:
        """Update the variants.barcode field in shProducts."""
        try:
            update_query = {"_id": product_id}
            update_operation = {
                "$set": {
                    "variants.barcode": barcode
                }
            }
            
            result = self.sh_products.update_one(update_query, update_operation)
            
            if result.modified_count > 0:
                logger.info(f"Successfully updated product {product_id} with barcode: {barcode}")
                return True
            else:
                logger.warning(f"No document was modified for product {product_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating product {product_id}: {e}")
            return False

    def process_single_product(self) -> bool:
        """Process one product and update its barcode using aggregation pipeline."""
        logger.info("Starting single product processing with aggregation pipeline...")

        # Find TCG products with catalog data using aggregation
        products = self.find_tcg_products_with_catalog()

        if not products:
            logger.error("No TCG products with valid catalog data found")
            return False

        # Process the first product
        product = products[0]
        product_id = product.get('_id')
        new_barcode = product.get('newBarcode')
        catalog_data = product.get('catalogData', {})

        logger.info(f"Processing product: {product_id}")
        logger.info(f"Product details: {product}")
        logger.info(f"Catalog UPC: {catalog_data.get('upc')}")
        logger.info(f"Catalog GTIN: {catalog_data.get('gtin')}")
        logger.info(f"Selected barcode: {new_barcode}")

        # Update the product
        success = self.update_product_barcode(product_id, new_barcode)

        if success:
            # Verify the update
            updated_product = self.sh_products.find_one({"_id": product_id})
            logger.info(f"Updated product verification: {updated_product}")
            logger.info(f"New barcode value: {updated_product.get('variants', {}).get('barcode')}")

        return success

    def process_all_products(self) -> bool:
        """Process all matching products using bulk operations."""
        logger.info("Starting bulk processing with aggregation pipeline...")

        # Find all TCG products with catalog data
        products = self.find_tcg_products_with_catalog()

        if not products:
            logger.error("No TCG products with valid catalog data found")
            return False

        logger.info(f"Found {len(products)} products to update")

        # Perform bulk update
        updated_count = self.bulk_update_barcodes(products)

        if updated_count > 0:
            logger.info(f"Successfully updated {updated_count} products")
            return True
        else:
            logger.error("No products were updated")
            return False

    def test_single_product_update(self, product_id: int) -> bool:
        """Test updating a single product by productId."""
        try:
            logger.info(f"Testing update for productId: {product_id}")

            # First, find the catalog record
            catalog_record = self.catalog.find_one({"productId": product_id})

            if not catalog_record:
                logger.error(f"No catalog record found for productId {product_id}")
                return False

            logger.info(f"Found catalog record: {catalog_record}")

            # Get UPC or GTIN (prefer UPC)
            upc = catalog_record.get('upc')
            gtin = catalog_record.get('gtin')

            barcode_value = None
            if upc and str(upc).strip() and str(upc) != "null":
                barcode_value = str(upc)
                logger.info(f"Using UPC: {barcode_value}")
            elif gtin and str(gtin).strip() and str(gtin) != "null":
                barcode_value = str(gtin)
                logger.info(f"Using GTIN: {barcode_value}")
            else:
                logger.error("No valid UPC or GTIN found in catalog record")
                return False

            # Find ALL matching shProducts records
            sh_products_string = list(self.sh_products.find({"productId": str(product_id)}))
            sh_products_int = list(self.sh_products.find({"productId": product_id}))

            # Combine both results and remove duplicates
            all_sh_products = []
            seen_ids = set()

            for product in sh_products_string + sh_products_int:
                if product["_id"] not in seen_ids:
                    all_sh_products.append(product)
                    seen_ids.add(product["_id"])

            if not all_sh_products:
                logger.error(f"No shProducts records found for productId {product_id}")
                return False

            logger.info(f"Found {len(all_sh_products)} shProducts records for productId {product_id}")

            updated_count = 0

            # Update each matching record
            for i, sh_product in enumerate(all_sh_products):
                logger.info(f"\nProcessing shProducts record {i+1}/{len(all_sh_products)}")
                logger.info(f"Record ID: {sh_product['_id']}")
                logger.info(f"Username: {sh_product.get('username', 'N/A')}")
                logger.info(f"Title: {sh_product.get('title', 'N/A')}")

                # Check current variants structure
                variants = sh_product.get('variants', [])
                if variants and len(variants) > 0:
                    current_barcode = variants[0].get('barcode', 'NOT SET')
                    logger.info(f"Current variants[0].barcode: {current_barcode}")
                else:
                    logger.info("No variants found in this shProducts record")
                    continue

                # Update the barcode in the first variant
                update_result = self.sh_products.update_one(
                    {"_id": sh_product["_id"]},
                    {"$set": {"variants.0.barcode": barcode_value}}
                )

                if update_result.modified_count > 0:
                    updated_count += 1
                    logger.info(f"✅ Successfully updated record {i+1} with barcode: {barcode_value}")

                    # Verify the update
                    updated_product = self.sh_products.find_one({"_id": sh_product["_id"]})
                    updated_variants = updated_product.get('variants', [])
                    if updated_variants and len(updated_variants) > 0:
                        new_barcode = updated_variants[0].get('barcode')
                        logger.info(f"Verification - New variants[0].barcode: {new_barcode}")
                else:
                    logger.error(f"❌ Failed to update record {i+1}")

            logger.info(f"\nSUMMARY: Updated {updated_count} out of {len(all_sh_products)} shProducts records")
            return updated_count > 0

        except Exception as e:
            logger.error(f"Error in test_single_product_update: {e}")
            return False

    def find_catalog_products_with_upc(self) -> int:
        """Find all catalog products with valid UPC field and return count."""
        try:
            pipeline = [
                # Match products where UPC exists and is not null/empty
                {
                    "$match": {
                        "upc": {
                            "$exists": True,
                            "$ne": None,
                            "$ne": "",
                            "$ne": "null"
                        }
                    }
                },
                # Count the results
                {
                    "$count": "total_with_upc"
                }
            ]

            result = list(self.catalog.aggregate(pipeline))
            count = result[0]["total_with_upc"] if result else 0

            logger.info(f"Found {count} catalog products with valid UPC field")

            # Also get some sample records to show what they look like
            sample_pipeline = [
                {
                    "$match": {
                        "upc": {
                            "$exists": True,
                            "$ne": None,
                            "$ne": "",
                            "$ne": "null"
                        }
                    }
                },
                {
                    "$limit": 5
                },
                {
                    "$project": {
                        "productId": 1,
                        "upc": 1,
                        "gtin": 1,
                        "name": 1
                    }
                }
            ]

            samples = list(self.catalog.aggregate(sample_pipeline))
            logger.info("Sample catalog products with UPC:")
            for sample in samples:
                logger.info(f"  ProductId: {sample.get('productId')}, UPC: {sample.get('upc')}, GTIN: {sample.get('gtin')}, Name: {sample.get('name', 'N/A')}")

            return count

        except Exception as e:
            logger.error(f"Error finding catalog products with UPC: {e}")
            return 0

    def close_connection(self):
        """Close the database connection."""
        if hasattr(self, 'client'):
            self.client.close()
            logger.info("Database connection closed")

def main():
    """Main function to run the barcode updater."""
    # MongoDB connection string with authentication
    connection_string = "*******************************************************************"

    updater = None
    try:
        updater = BarcodeUpdater(connection_string)

        # Test with specific productId 534039
        test_product_id = 534039
        logger.info(f"Testing barcode update for productId: {test_product_id}")

        success = updater.test_single_product_update(test_product_id)

        if success:
            logger.info(f"SUCCESS: Updated barcode for productId {test_product_id}")
        else:
            logger.error(f"FAILED: Could not update barcode for productId {test_product_id}")

        # Optionally, also show catalog UPC count
        logger.info("\n" + "="*50)
        logger.info("Finding catalog products with valid UPC field...")
        upc_count = updater.find_catalog_products_with_upc()

        if upc_count > 0:
            logger.info(f"INFO: Found {upc_count} total catalog products with valid UPC field")
        else:
            logger.error("No catalog products with valid UPC found")

    except Exception as e:
        logger.error(f"Script execution failed: {e}")
    finally:
        if updater:
            updater.close_connection()

if __name__ == "__main__":
    main()
