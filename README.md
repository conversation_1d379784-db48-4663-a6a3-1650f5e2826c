# Barcode Updater Script

This Python script connects to a MongoDB database and updates barcode information in the `shProducts` collection based on data from the `catalog` collection.

## What the script does:

1. **Connects** to the MongoDB `test` database
2. **Finds** all records in `shProducts` where `tcgItem` is `true`
3. **Filters** those records to only include ones where `isSealed` is `true`
4. **Looks up** corresponding records in the `catalog` collection using `productId` as an integer
5. **Updates** the `variants.barcode` field in `shProducts` with either the `upc` or `gtin` from the catalog record
6. **Saves** the barcode as a string
7. **Provides** detailed logging of the entire process

## Setup:

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Make sure your MongoDB server is running and accessible

3. Update the connection string in the script if needed (default is `mongodb://localhost:27017/`)

## Usage:

```bash
python barcode_updater.py
```

## Features:

- **Full logging**: All operations are logged both to console and to `barcode_update.log`
- **Error handling**: Comprehensive error handling with detailed error messages
- **Single product processing**: Processes one product at a time for testing
- **Verification**: Shows the updated record after modification
- **Preference for UPC**: Uses UPC over GTIN when both are available

## Output:

The script will show:
- Connection status
- Number of matching products found
- Details of the product being processed
- Catalog record details
- The barcode value being used (UPC or GTIN)
- Confirmation of the update
- Verification of the updated record

## Customization:

To process all matching products instead of just one, modify the `process_single_product()` method to loop through all products in the list.
